#!/usr/bin/env python3
"""
Application CLI pour analyser et renommer les tickets de caisse
"""

import os
import sys
import json
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import click
import pytesseract
from pdf2image import convert_from_path
import openai
from datetime import datetime
import re

# Load environment variables from .env file
def load_env():
    """Load environment variables from .env file"""
    env_path = Path('.env')
    if env_path.exists():
        with open(env_path, 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    os.environ[key.strip()] = value.strip()

# Load .env at startup
load_env()

class ReceiptAnalyzer:
    def __init__(self, api_key: str, use_ollama: bool = False, ollama_endpoint: str = None):
        """Initialize the receipt analyzer with API configuration"""
        self.use_ollama = use_ollama

        if use_ollama:
            # Configure for Ollama
            self.client = openai.OpenAI(
                api_key=api_key or "ollama",  # Ollama doesn't need a real API key
                base_url=ollama_endpoint or "https://isidor.ca.konvergo.ai/ollama/v1"
            )
            self.model = "gpt-oss-20b"
            print("🤖 Utilisation d'Ollama avec le modèle gpt-oss-20b")
        else:
            # Configure for OpenAI
            self.client = openai.OpenAI(api_key=api_key)
            self.model = "gpt-4o-mini"
            print("🤖 Utilisation d'OpenAI GPT-4o-mini")
    
    def extract_text_from_pdf(self, pdf_path: Path) -> str:
        """Extract text from PDF - try direct text extraction first, then OCR"""
        try:
            print(f"📄 Extraction du texte de {pdf_path.name}...")

            # First try direct text extraction (for PDFs with selectable text)
            try:
                import PyPDF2
                with open(pdf_path, 'rb') as file:
                    pdf_reader = PyPDF2.PdfReader(file)
                    text = ""
                    for page in pdf_reader.pages:
                        text += page.extract_text()

                    if text.strip() and len(text.strip()) > 50:  # If we got meaningful text
                        print(f"✅ Texte extrait directement ({len(text)} caractères)")
                        return text.strip()

            except Exception:
                pass  # Fall back to OCR

            # Fall back to OCR for scanned PDFs
            print("🔍 Utilisation de l'OCR pour PDF scanné...")
            images = convert_from_path(pdf_path)

            if not images:
                return ""

            # Try OCR with different orientations to find the best one
            best_text = ""
            best_confidence = 0
            best_orientation = 0

            for angle in [0, 90, 180, 270]:
                try:
                    # Rotate image if needed
                    img = images[0]
                    if angle != 0:
                        img = img.rotate(angle, expand=True)

                    # Get OCR with confidence data
                    data = pytesseract.image_to_data(img, lang='fra', output_type=pytesseract.Output.DICT)
                    confidences = [int(conf) for conf in data['conf'] if int(conf) > 0]
                    avg_confidence = sum(confidences) / len(confidences) if confidences else 0

                    # Get text
                    text = pytesseract.image_to_string(img, lang='fra')

                    # Score this orientation (confidence + text length)
                    score = avg_confidence + (len(text.strip()) / 10)

                    if score > best_confidence and len(text.strip()) > 20:
                        best_text = text.strip()
                        best_confidence = score
                        best_orientation = angle

                except Exception:
                    continue

            if best_orientation != 0:
                print(f"🔄 Meilleure orientation trouvée: {best_orientation}° (confiance: {best_confidence:.1f})")

            if best_text:
                print(f"✅ Texte extrait par OCR ({len(best_text)} caractères)")
            return best_text

        except Exception as e:
            print(f"❌ Erreur lors de l'extraction: {e}")
            return ""
    
    def analyze_receipt_with_llm(self, text: str) -> Dict:
        """Analyze receipt text using OpenAI to extract store name and date"""
        current_year = datetime.now().year
        prompt = f"""
Analyse ce ticket de caisse et extrait les informations suivantes:

TEXTE DU TICKET:
{text}

Réponds UNIQUEMENT avec un JSON valide contenant:
{{
    "store_name": "nom du magasin normalisé (ex: IKEA, CARREFOUR, LECLERC)",
    "date": "date au format YYYYMMDD",
    "confidence_store": "score de 0 à 100 pour la fiabilité du nom du magasin",
    "confidence_date": "score de 0 à 100 pour la fiabilité de la date",
    "raw_store": "nom brut trouvé sur le ticket",
    "raw_date": "date brute trouvée sur le ticket"
}}

Règles IMPORTANTES:
- Normalise les noms de magasins (IKAE -> IKEA, etc.)
- Convertis toutes les dates au format YYYYMMDD
- ATTENTION: Nous sommes en {current_year}. Les tickets de caisse ne peuvent PAS être du futur
- Si l'OCR donne une année > {current_year}, c'est une ERREUR d'OCR à corriger
- Exemples d'erreurs OCR courantes: 2075 -> 2025, 2024 -> 2024, 2023 -> 2023
- Les tickets peuvent être de {current_year-5} à {current_year} maximum
- Si la date semble impossible (futur ou trop ancienne), essaie de la corriger intelligemment
- Si tu ne peux pas corriger la date, mets confidence_date à 0
- Si tu ne trouves pas d'info, mets null
- Donne un score de confiance réaliste
"""

        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[{"role": "user", "content": prompt}],
                temperature=0.1
            )
            
            result_text = response.choices[0].message.content.strip()
            
            # Extract JSON from response (in case there's extra text)
            json_match = re.search(r'\{.*\}', result_text, re.DOTALL)
            if json_match:
                result_text = json_match.group()
            
            result = json.loads(result_text)
            return result
            
        except Exception as e:
            print(f"❌ Erreur lors de l'analyse LLM: {e}")
            return {
                "store_name": None,
                "date": None,
                "confidence_store": 0,
                "confidence_date": 0,
                "raw_store": None,
                "raw_date": None
            }

    def validate_and_correct_date(self, analysis: Dict) -> Dict:
        """Validate and correct date if needed"""
        if not analysis.get("date"):
            return analysis

        try:
            date_str = analysis["date"]
            if len(date_str) == 8 and date_str.isdigit():
                year = int(date_str[:4])
                month = int(date_str[4:6])
                day = int(date_str[6:8])

                current_year = datetime.now().year

                # Check if year is in the future or too far in the past
                if year > current_year:
                    print(f"⚠️  Date future détectée ({year}) - correction nécessaire")
                    # Try common OCR corrections
                    if year == 2075:  # Common OCR error
                        corrected_year = 2025
                    elif year == 2074:
                        corrected_year = 2024
                    elif year == 2073:
                        corrected_year = 2023
                    else:
                        # Default to current year
                        corrected_year = current_year

                    analysis["date"] = f"{corrected_year:04d}{month:02d}{day:02d}"
                    analysis["confidence_date"] = max(50, analysis["confidence_date"] - 20)
                    print(f"✅ Date corrigée: {year} -> {corrected_year}")

                elif year < current_year - 10:  # Too old (more than 10 years)
                    print(f"⚠️  Date trop ancienne ({year}) - confiance réduite")
                    analysis["confidence_date"] = max(30, analysis["confidence_date"] - 30)

                # Validate month and day
                if month < 1 or month > 12:
                    print(f"⚠️  Mois invalide ({month}) - confiance réduite")
                    analysis["confidence_date"] = max(20, analysis["confidence_date"] - 40)

                if day < 1 or day > 31:
                    print(f"⚠️  Jour invalide ({day}) - confiance réduite")
                    analysis["confidence_date"] = max(20, analysis["confidence_date"] - 40)

        except (ValueError, IndexError):
            print(f"⚠️  Format de date invalide - confiance réduite")
            analysis["confidence_date"] = 0

        return analysis
    
    def generate_new_filename(self, analysis: Dict, original_path: Path, existing_files: List[Path]) -> str:
        """Generate new filename based on analysis results"""
        if not analysis["store_name"] or not analysis["date"]:
            return f"UNKNOWN_{original_path.stem}"
        
        base_name = f"{analysis['date']} {analysis['store_name']}"
        extension = original_path.suffix
        
        # Check for duplicates and add part number if needed
        new_filename = f"{base_name}{extension}"
        counter = 1
        
        while any(f.name == new_filename for f in existing_files):
            counter += 1
            new_filename = f"{base_name} partie {counter}{extension}"
        
        return new_filename
    
    def process_file(self, pdf_path: Path, output_dir: Path, existing_files: List[Path]) -> Dict:
        """Process a single PDF file"""
        print(f"\n🔍 Traitement de {pdf_path.name}")
        
        # Extract text
        text = self.extract_text_from_pdf(pdf_path)
        if not text:
            print("❌ Aucun texte extrait")
            return {"success": False, "error": "No text extracted"}
        
        print(f"✅ Texte extrait ({len(text)} caractères)")
        
        # Analyze with LLM
        analysis = self.analyze_receipt_with_llm(text)

        # Validate and correct date if needed
        analysis = self.validate_and_correct_date(analysis)

        # Display results
        print(f"🏪 Magasin: {analysis['store_name']} (confiance: {analysis['confidence_store']}%)")
        print(f"📅 Date: {analysis['date']} (confiance: {analysis['confidence_date']}%)")
        
        # Generate new filename
        new_filename = self.generate_new_filename(analysis, pdf_path, existing_files)
        
        return {
            "success": True,
            "original_name": pdf_path.name,
            "new_name": new_filename,
            "analysis": analysis,
            "text_preview": text[:200] + "..." if len(text) > 200 else text
        }

@click.command()
@click.option('--input-dir', '-i', default='src', help='Dossier contenant les PDF à traiter')
@click.option('--output-dir', '-o', default='renamed', help='Dossier de sortie pour les fichiers renommés')
@click.option('--api-key', envvar='OPENAI_API_KEY', help='Clé API OpenAI (ou variable OPENAI_API_KEY)')
@click.option('--ollama', is_flag=True, help='Utiliser Ollama au lieu d\'OpenAI')
@click.option('--ollama-endpoint', default='https://isidor.ca.konvergo.ai/ollama/v1', help='Endpoint Ollama personnalisé')
@click.option('--ollama-key', envvar='OLLAMA_API_KEY', help='Clé API Ollama (optionnelle)')
@click.option('--dry-run', is_flag=True, help='Simulation sans renommer les fichiers')
@click.option('--min-confidence', default=70, help='Confiance minimale pour renommer automatiquement')
def main(input_dir: str, output_dir: str, api_key: str, ollama: bool, ollama_endpoint: str, ollama_key: str, dry_run: bool, min_confidence: int):
    """Analyser et renommer les tickets de caisse PDF"""

    # Determine which API to use and validate keys
    if ollama:
        # Use Ollama
        final_api_key = ollama_key or "ollama"  # Ollama might not need a real key
        print(f"🔗 Utilisation d'Ollama: {ollama_endpoint}")
    else:
        # Use OpenAI
        if not api_key:
            print("❌ Clé API OpenAI requise. Utilisez --api-key ou la variable OPENAI_API_KEY")
            print("💡 Ou utilisez --ollama pour utiliser Ollama à la place")
            sys.exit(1)
        final_api_key = api_key
    
    input_path = Path(input_dir)
    output_path = Path(output_dir)
    
    if not input_path.exists():
        print(f"❌ Dossier d'entrée {input_path} introuvable")
        sys.exit(1)
    
    # Create output directory
    if not dry_run:
        output_path.mkdir(exist_ok=True)
    
    # Find PDF files
    pdf_files = list(input_path.glob("*.pdf"))
    if not pdf_files:
        print(f"❌ Aucun fichier PDF trouvé dans {input_path}")
        sys.exit(1)
    
    print(f"📁 Trouvé {len(pdf_files)} fichier(s) PDF")
    
    # Initialize analyzer
    analyzer = ReceiptAnalyzer(
        api_key=final_api_key,
        use_ollama=ollama,
        ollama_endpoint=ollama_endpoint if ollama else None
    )
    
    # Track existing files for duplicate handling
    existing_files = []
    results = []
    
    for pdf_file in pdf_files:
        try:
            result = analyzer.process_file(pdf_file, output_path, existing_files)
            results.append(result)
            
            if result["success"]:
                analysis = result["analysis"]
                min_conf = min(analysis["confidence_store"], analysis["confidence_date"])
                
                if min_conf >= min_confidence:
                    if dry_run:
                        print(f"🔄 [DRY RUN] {result['original_name']} → {result['new_name']}")
                    else:
                        # Rename file
                        new_path = output_path / result['new_name']
                        pdf_file.rename(new_path)
                        existing_files.append(new_path)
                        print(f"✅ Renommé: {result['new_name']}")
                else:
                    print(f"⚠️  Confiance trop faible ({min_conf}%) - fichier non renommé")
            
        except Exception as e:
            print(f"❌ Erreur lors du traitement de {pdf_file.name}: {e}")
    
    # Summary
    print(f"\n📊 RÉSUMÉ:")
    successful = sum(1 for r in results if r["success"])
    print(f"✅ {successful}/{len(pdf_files)} fichiers traités avec succès")
    
    if dry_run:
        print("🔍 Mode simulation - aucun fichier n'a été renommé")

if __name__ == "__main__":
    main()
