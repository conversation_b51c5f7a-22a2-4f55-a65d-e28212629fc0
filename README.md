# Analyseur de Tickets de Caisse

Application CLI pour analyser automatiquement les tickets de caisse PDF et les renommer selon la nomenclature : `YYYYMMDD Nom du magasin`

## Installation

```bash
./setup.sh
```

## Configuration

### Option 1: OpenAI (par défaut)
Modifiez le fichier `.env` et remplacez la clé par votre vraie clé OpenAI :
```
OPENAI_API_KEY=sk-votre-vraie-clé-openai
```

### Option 2: Ollama (alternative)
Pour utiliser Ollama avec le modèle gpt-oss :
```
# Optionnel dans .env
OLLAMA_API_KEY=votre-clé-ollama-si-nécessaire
```

## Utilisation

### Avec OpenAI (par défaut)
```bash
# Mode simulation
python3.11 receipt_analyzer.py --dry-run

# Mode réel
python3.11 receipt_analyzer.py
```

### Avec Ollama
```bash
# Mode simulation avec Ollama
python3.11 receipt_analyzer.py --ollama --dry-run

# Mode réel avec Ollama
python3.11 receipt_analyzer.py --ollama
```

### Options disponibles
- `--input-dir` : Dossier contenant les PDF (défaut: `src`)
- `--output-dir` : Dossier de sortie (défaut: `renamed`)
- `--ollama` : Utiliser Ollama au lieu d'OpenAI
- `--ollama-endpoint` : Endpoint Ollama personnalisé (défaut: https://isidor.ca.konvergo.ai/ollama/v1)
- `--dry-run` : Mode simulation sans renommer
- `--min-confidence` : Confiance minimale pour renommer (défaut: 70%)

## Exemples

```bash
# Avec OpenAI - analyser avec confiance minimale de 80%
python3.11 receipt_analyzer.py -i tickets --min-confidence 80

# Avec Ollama - mode simulation
python3.11 receipt_analyzer.py --ollama --dry-run

# Avec Ollama - endpoint personnalisé
python3.11 receipt_analyzer.py --ollama --ollama-endpoint http://localhost:11434/v1

# Comparer les deux APIs en mode simulation
python3.11 receipt_analyzer.py --dry-run          # OpenAI
python3.11 receipt_analyzer.py --ollama --dry-run # Ollama
```

## Fonctionnalités

- ✅ Extraction OCR du texte des PDF scannés
- ✅ Analyse intelligente avec GPT pour identifier magasin et date
- ✅ Normalisation des noms de magasins (IKAE → IKEA)
- ✅ Gestion des doublons (partie 1, partie 2, etc.)
- ✅ Score de confiance pour chaque extraction
- ✅ Mode simulation pour tester avant renommage
