# Analyseur de Tickets de Caisse

Application CLI pour analyser automatiquement les tickets de caisse PDF et les renommer selon la nomenclature : `YYYYMMDD Nom du magasin`

## Installation

```bash
./setup.sh
```

## Configuration

Modifiez le fichier `.env` et remplacez `votre-clé-api-ici` par votre vraie clé OpenAI :
```
OPENAI_API_KEY=sk-votre-vraie-clé-openai
```

Ou définissez la variable d'environnement :
```bash
export OPENAI_API_KEY='votre-clé-api-openai'
```

## Utilisation

### Mode test (simulation)
```bash
python3 receipt_analyzer.py --dry-run
```

### Mode réel
```bash
python3 receipt_analyzer.py
```

### Options disponibles
- `--input-dir` : Dossier contenant les PDF (défaut: `src`)
- `--output-dir` : <PERSON><PERSON><PERSON> de sortie (défaut: `renamed`)
- `--dry-run` : Mode simulation sans renommer
- `--min-confidence` : Confiance minimale pour renommer (défaut: 70%)

## Exemples

```bash
# Analyser les PDF du dossier 'tickets' avec confiance minimale de 80%
python3 receipt_analyzer.py -i tickets --min-confidence 80

# Mode simulation pour voir ce qui serait renommé
python3 receipt_analyzer.py --dry-run
```

## Fonctionnalités

- ✅ Extraction OCR du texte des PDF scannés
- ✅ Analyse intelligente avec GPT pour identifier magasin et date
- ✅ Normalisation des noms de magasins (IKAE → IKEA)
- ✅ Gestion des doublons (partie 1, partie 2, etc.)
- ✅ Score de confiance pour chaque extraction
- ✅ Mode simulation pour tester avant renommage
