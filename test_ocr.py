#!/usr/bin/env python3

import pytesseract
from pdf2image import convert_from_path
import sys

def test_ocr(pdf_path):
    """Test OCR on a PDF file"""
    try:
        # Convert PDF to images
        print(f"Converting {pdf_path} to images...")
        images = convert_from_path(pdf_path)
        
        print(f"Found {len(images)} page(s)")
        
        # Extract text from first page
        if images:
            print("\n=== OCR Text from first page ===")
            text = pytesseract.image_to_string(images[0], lang='fra')
            print(text)
            print("=== End OCR Text ===\n")
            
            return text
        else:
            print("No images found in PDF")
            return None
            
    except Exception as e:
        print(f"Error: {e}")
        return None

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python test_ocr.py <pdf_file>")
        sys.exit(1)
    
    pdf_file = sys.argv[1]
    test_ocr(pdf_file)
