#!/bin/bash

echo "🚀 Installation de l'analyseur de tickets de caisse"

# Install system dependencies
echo "📦 Installation des dépendances système..."
sudo apt update
sudo apt install -y tesseract-ocr tesseract-ocr-fra poppler-utils

# Install Python dependencies
echo "🐍 Installation des dépendances Python..."
pip install -r requirements.txt

echo "✅ Installation terminée!"
echo ""
echo "📋 Pour utiliser l'application:"
echo "1. Définissez votre clé API OpenAI:"
echo "   export OPENAI_API_KEY='votre-clé-api'"
echo ""
echo "2. <PERSON>z l'analyse (mode test):"
echo "   python3 receipt_analyzer.py --dry-run"
echo ""
echo "3. Lancez l'analyse réelle:"
echo "   python3 receipt_analyzer.py"
