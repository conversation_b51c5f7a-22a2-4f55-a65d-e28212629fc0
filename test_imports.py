#!/usr/bin/env python3

print("Testing imports...")

try:
    import sys
    print(f"✅ Python version: {sys.version}")
except:
    print("❌ Python import failed")

try:
    import os
    print("✅ os imported")
except:
    print("❌ os import failed")

try:
    import click
    print("✅ click imported")
except Exception as e:
    print(f"❌ click import failed: {e}")

try:
    import openai
    print("✅ openai imported")
except Exception as e:
    print(f"❌ openai import failed: {e}")

try:
    import pytesseract
    print("✅ pytesseract imported")
except Exception as e:
    print(f"❌ pytesseract import failed: {e}")

try:
    from pdf2image import convert_from_path
    print("✅ pdf2image imported")
except Exception as e:
    print(f"❌ pdf2image import failed: {e}")

print("\nDone testing imports.")
